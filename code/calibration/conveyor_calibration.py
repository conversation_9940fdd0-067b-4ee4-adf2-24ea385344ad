from loguru import logger
import cv2 as cv
import numpy as np
import tomlkit

from calibration.aruco_detection import detect_aruco
from calibration.points_to_transform import get_transform
from camera.camera_realsense import RealSenseCamera


class ConveyorCalibration:
    def __init__(self, camera):
        # read conveyor.toml
        with open("config/conveyor.toml", "r") as f:
            self.config = tomlkit.load(f)
        self.marker_ids: dict[str, int] = self.config.item("markers").unwrap()
        self.camera = camera

    def run(self):
        with self.camera:
            for _ in range(20):
                frames = self.camera.get_frames()  # Warm-up (temporal filter, white balance, ...)

            recording = False
            frames_to_record = 100
            recorded_coordinates = {id: [] for id in self.marker_ids.values()}

            while True:
                frames = self.camera.get_frames()
                if frames is None:
                    logger.warning("Failed to get frames")
                    continue
                color_image, depth_image, vertices = frames
                if color_image.shape[:2] != depth_image.shape[:2]:
                    logger.error(
                        f"Color and depth image shapes do not match: {color_image.shape} vs {depth_image.shape}"
                        "This is probably because the decimation filter is enabled. Disable decimation."
                    )
                    raise RuntimeError(
                        "Color and depth image shapes do not match. Disable decimation."
                    )

                def text(text, position, color=(0, 255, 0)):
                    cv.putText(
                        color_image,
                        text,
                        tuple(position.astype(int))
                        if isinstance(position, np.ndarray)
                        else position,
                        cv.FONT_HERSHEY_SIMPLEX,
                        1.0,
                        color,
                        2,
                    )

                corners, ids = detect_aruco(color_image)
                if ids is not None:
                    for i, corner in enumerate(corners):
                        id = ids[i][0]
                        corner = corner[0]
                        if id not in self.marker_ids.values():
                            continue

                        if recording:
                            # Save corner 0
                            coordinate = vertices[tuple(corner[0, ::-1].astype(int))]
                            recorded_coordinates[id].append(coordinate)

                        cv.polylines(color_image, [corner.astype(int)], True, (255, 0, 0), 4)
                        cv.circle(color_image, tuple(corner[0, :].astype(int)), 3, (0, 0, 255), -1)
                        text_position = np.array(corner[0]).astype(int)
                        text(f"{id}", text_position + [40, 0])
                        text(
                            f"{vertices[tuple(corner[0, ::-1].astype(int))]}",
                            text_position + [-200, 40],
                        )

                if not recording:
                    text("Press space to start recording", (10, 30))
                else:
                    text(
                        f"Recording... {frames_to_record} frames left", (10, 30), color=(0, 0, 255)
                    )
                    frames_to_record -= 1
                    if frames_to_record <= 0:
                        break

                cv.imshow("Color", color_image)
                cv.imshow("Depth", vertices[:, :, 2])
                # cv.imshow("Depth", depth_image)

                key = cv.waitKey(1)
                if key == 27:  # ESC
                    exit(0)
                if key == 32:  # SPACE
                    recording = not recording
                    if recording:
                        frames_to_record = 100
                        recorded_coordinates = {id: [] for id in self.marker_ids.values()}

            resulting_coordinates = {
                "upstream": None,
                "width": None,
                "downstream": None,
            }

            for id, data in recorded_coordinates.items():
                mean = np.mean(data, axis=0)
                std = np.std(data, axis=0)
                if len(data) < 100:
                    logger.opt(colors=True).warning(
                        f"<red>Marker {id}: {len(data)} frames recorded < 95!</red>"
                    )
                else:
                    logger.info(f"Marker {id}: {len(data)} frames recorded")
                logger.info("  Average position:   " + (" ".join(f"{x: .6f}" for x in mean)))
                if np.any(std > 0.0015):
                    logger.opt(colors=True).warning(
                        "  <red>Standard deviation: "
                        + (" ".join(f"{x: .6f}" for x in std))
                        + " > 1.5 mm!</red>"
                    )
                else:
                    logger.info("  Standard deviation: " + (" ".join(f"{x: .6f}" for x in std)))
                if id == self.marker_ids["upstream_marker"]:
                    resulting_coordinates["upstream"] = mean
                elif id == self.marker_ids["width_marker"]:
                    resulting_coordinates["width"] = mean
                elif id == self.marker_ids["downstream_marker"]:
                    resulting_coordinates["downstream"] = mean

            assert resulting_coordinates["upstream"] is not None
            assert resulting_coordinates["width"] is not None
            assert resulting_coordinates["downstream"] is not None

            transform = get_transform(
                resulting_coordinates["upstream"],
                resulting_coordinates["downstream"] - resulting_coordinates["upstream"],
                resulting_coordinates["width"] - resulting_coordinates["upstream"],
            )

            # Find ROI distances: for x this is the distance between upstream and downstream marker
            # For y this is the distance between upstream and width marker (but only in the y direction)
            upstream_new = transform[:3, :3] @ resulting_coordinates["upstream"] + transform[:3, 3]
            width_new = transform[:3, :3] @ resulting_coordinates["width"] + transform[:3, 3]
            downstream_new = (
                transform[:3, :3] @ resulting_coordinates["downstream"] + transform[:3, 3]
            )
            x_distance = downstream_new[0] - upstream_new[0]
            y_distance = width_new[1] - upstream_new[1]

            logger.info(f"ROI: {x_distance:.3f} x {y_distance:.3f} m")

            if x_distance < 0 or y_distance < 0:
                logger.error("ROI negative!")
            elif x_distance < 0.5 or y_distance < 0.5:
                logger.warning("ROI too small!")

            # Translate "marker thickness" in the new negative z direction
            marker_thickness: np.float16 = self.config["height"]["marker_thickness"]  # type: ignore
            transform[:3, 3] -= transform[:3, 2] * marker_thickness

            answer = input("Save to conveyor.toml? (y/n)")
            if answer == "y":
                # Update transform in calibration section (overwrites if exists)
                self.config["calibration"]["transform"] = transform.tolist()  # type: ignore
                self.config["calibration"]["roi"] = [x_distance, y_distance]  # type: ignore
                with open("config/conveyor.toml", "w") as f:
                    tomlkit.dump(self.config, f)

            logger.debug(transform.tolist())


if __name__ == "__main__":
    camera = RealSenseCamera(
        enable_decimation=False,
        enable_spatial_filter=False,
        enable_temporal_filter=False,
        enable_hole_filling=False,
    )
    conveyor_calibration = ConveyorCalibration(camera)

    conveyor_calibration.run()
